# MPV2 Roadmap

This document outlines the features that need to be implemented in MPV2.

## Features

- [ ] Automated Cold Calling
- [ ] Item Flipping (such as sneakers)
- [ ] Create a Short based on long-form content
- [ ] Subtitles for Shorts

## Adding a new feature

If you want to add a new feature to MPV2, please create a new issue and label it with `enhancement`. After that, create a new branch and start working on the feature. Once you are done, create a pull request and assign it to the issue you created earlier.

## Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct, and the process for submitting pull requests to us.